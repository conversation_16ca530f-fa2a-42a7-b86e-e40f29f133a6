'use client'

import { useTranslations } from 'next-intl'
import { ShoppingCart, ArrowRight, X } from 'lucide-react'
import Link from 'next/link'
import { useLocale } from 'next-intl'
import { useEffect, useCallback } from 'react'

import {
  Sheet,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>ooter,
  SheetClose,
} from '@/components/ui/sheet'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'

import { useCartDrawer } from '../store'
import { useCartQuery, useCartSummary } from '../queries'
import { CartItemV2 } from './cart-item'
import { CartSummaryV2 } from './cart-summary'
import { EmptyCartV2 } from './empty-cart'
import { CartLoadingV2 } from './cart-loading'
import { CartErrorV2 } from './cart-error'

export function CartDrawerV2() {
  const t = useTranslations('cart')
  const locale = useLocale()
  const { isOpen, setOpen } = useCartDrawer()
  const { data: cart, isLoading, error } = useCartQuery()
  const { data: summary } = useCartSummary()

  const itemCount = cart?.items?.length || 0
  const totalQuantity = cart?.items?.reduce((sum, item) => sum + item.quantity, 0) || 0

  // Handle keyboard navigation
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (event.key === 'Escape' && isOpen) {
      setOpen(false)
    }
  }, [isOpen, setOpen])

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [handleKeyDown])

  // Prevent body scroll when drawer is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isOpen])

  return (
    <Sheet open={isOpen} onOpenChange={setOpen}>
      <SheetContent
        className="w-full sm:max-w-lg flex flex-col focus:outline-none"
        aria-labelledby="cart-drawer-title"
        aria-describedby="cart-drawer-description"
      >
        <SheetHeader className="space-y-2.5">
          <div className="flex items-center justify-between">
            <SheetTitle
              id="cart-drawer-title"
              className="flex items-center gap-2 text-lg font-semibold"
            >
              <ShoppingCart className="h-5 w-5" aria-hidden="true" />
              {t('title')}
            </SheetTitle>
            <div className="flex items-center gap-2">
              {totalQuantity > 0 && (
                <Badge
                  variant="secondary"
                  className="animate-in fade-in-0 zoom-in-95 duration-200"
                  aria-label={t('itemCount', { count: totalQuantity })}
                >
                  {totalQuantity}
                </Badge>
              )}
              <SheetClose asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 rounded-full"
                  aria-label={t('close')}
                >
                  <X className="h-4 w-4" />
                </Button>
              </SheetClose>
            </div>
          </div>
          <p
            id="cart-drawer-description"
            className="text-sm text-muted-foreground sr-only"
          >
            {t('drawerDescription')}
          </p>
        </SheetHeader>

        <Separator className="my-4" />

        {/* Cart Content */}
        <div className="flex-1 overflow-hidden flex flex-col min-h-0">
          {isLoading ? (
            <div role="status" aria-label={t('loading')}>
              <CartLoadingV2 />
            </div>
          ) : error ? (
            <div role="alert" aria-live="polite">
              <CartErrorV2 error={error} />
            </div>
          ) : !cart || itemCount === 0 ? (
            <EmptyCartV2 onClose={() => setOpen(false)} />
          ) : (
            <>
              {/* Cart Items */}
              <ScrollArea className="flex-1 pr-4">
                <div
                  className="space-y-4 animate-in fade-in-0 duration-300"
                  role="list"
                  aria-label={t('cartItems')}
                >
                  {cart.items.map((item, index) => (
                    <div
                      key={item.id}
                      role="listitem"
                      className="animate-in slide-in-from-right-2 duration-300"
                      style={{ animationDelay: `${index * 50}ms` }}
                    >
                      <CartItemV2 item={item} />
                    </div>
                  ))}
                </div>
              </ScrollArea>

              <Separator className="my-4" />

              {/* Cart Summary */}
              <div className="space-y-4 animate-in slide-in-from-bottom-2 duration-300">
                <CartSummaryV2 summary={summary} />
              </div>
            </>
          )}
        </div>

        {/* Footer Actions */}
        {cart && itemCount > 0 && (
          <>
            <Separator className="my-4" />
            <SheetFooter className="space-y-2 animate-in slide-in-from-bottom-2 duration-300">
              <Button
                asChild
                className="w-full transition-all duration-200 hover:scale-[1.02] active:scale-[0.98]"
                size="lg"
                onClick={() => setOpen(false)}
              >
                <Link href={`/${locale}/checkout`}>
                  {t('proceedToCheckout')}
                  <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                </Link>
              </Button>
              <Button
                variant="outline"
                asChild
                className="w-full transition-all duration-200 hover:scale-[1.02] active:scale-[0.98]"
                onClick={() => setOpen(false)}
              >
                <Link href={`/${locale}/cart`}>
                  {t('viewFullCart')}
                </Link>
              </Button>
            </SheetFooter>
          </>
        )}
      </SheetContent>
    </Sheet>
  )
}
