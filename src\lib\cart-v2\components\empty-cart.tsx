'use client'

import { useTranslations, useLocale } from 'next-intl'
import { ShoppingCart, Coffee, Package } from 'lucide-react'
import Link from 'next/link'

import { Button } from '@/components/ui/button'

interface EmptyCartV2Props {
  onClose?: () => void
  compact?: boolean
}

export function EmptyCartV2({ onClose, compact = false }: EmptyCartV2Props) {
  const t = useTranslations('cart')
  const locale = useLocale()

  return (
    <div className={`flex flex-col items-center justify-center text-center space-y-4 ${
      compact ? 'py-8' : 'py-12'
    }`}>
      <div className="relative">
        <ShoppingCart className={`text-muted-foreground ${
          compact ? 'h-16 w-16' : 'h-20 w-20'
        }`} />
        <div className="absolute -top-1 -right-1 animate-bounce">
          <Coffee className="h-6 w-6 text-primary" />
        </div>
      </div>
      
      <div className="space-y-2">
        <h3 className={`font-semibold ${compact ? 'text-lg' : 'text-xl'}`}>
          {t('empty')}
        </h3>
        <p className={`text-muted-foreground max-w-sm ${compact ? 'text-sm' : ''}`}>
          {t('emptyDescription')}
        </p>
      </div>

      <div className={`flex flex-col gap-3 w-full max-w-xs ${compact ? 'gap-2' : ''}`}>
        <Button
          asChild
          size={compact ? 'sm' : 'default'}
          className="w-full"
          onClick={onClose}
        >
          <Link href={`/${locale}/coffee-box-builder`}>
            <Package className="mr-2 h-4 w-4" />
            {t('coffeeBoxBuilder')}
          </Link>
        </Button>
        
        <Button
          variant="outline"
          asChild
          size={compact ? 'sm' : 'default'}
          className="w-full"
          onClick={onClose}
        >
          <Link href={`/${locale}/shop`}>
            <ShoppingCart className="mr-2 h-4 w-4" />
            {t('continueShopping')}
          </Link>
        </Button>
      </div>
    </div>
  )
}
