'use client'

import { useState } from 'react'
import { useTranslations } from 'next-intl'
import { ShoppingCart, Check, Loader2, Plus } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { useToast } from '@/hooks/use-toast'

import { useAddToCartMutation } from '../queries'
import { useCartDrawerActions, useLastAddedItem } from '../store'

interface AddToCartButtonV2Props {
  productId: string
  quantity?: number
  variant?: 'default' | 'outline' | 'ghost' | 'secondary'
  size?: 'sm' | 'default' | 'lg'
  className?: string
  children?: React.ReactNode
  showSuccessState?: boolean
  openDrawerOnAdd?: boolean
  disabled?: boolean
}

export function AddToCartButtonV2({
  productId,
  quantity = 1,
  variant = 'default',
  size = 'default',
  className,
  children,
  showSuccessState = true,
  openDrawerOnAdd = true,
  disabled = false,
}: AddToCartButtonV2Props) {
  const t = useTranslations('common')
  const { toast } = useToast()
  const [showSuccess, setShowSuccess] = useState(false)

  const addToCartMutation = useAddToCartMutation()
  const { openDrawer } = useCartDrawerActions()
  const { item: lastAddedItem } = useLastAddedItem()

  const isLoading = addToCartMutation.isPending
  const isDisabled = disabled || isLoading

  const handleAddToCart = async () => {
    if (isDisabled) return

    try {
      await addToCartMutation.mutateAsync({
        product_id: productId,
        quantity,
      })

      // Show success state
      if (showSuccessState) {
        setShowSuccess(true)
        setTimeout(() => setShowSuccess(false), 2000)
      }

      // Show success toast
      toast({
        title: t('added'),
        description: t('productAddedToCart'),
      })

      // Open cart drawer if requested
      if (openDrawerOnAdd) {
        // Small delay to allow optimistic update to complete
        setTimeout(() => openDrawer(), 100)
      }
    } catch (error) {
      // Error handling is done in the mutation's onError callback
      console.error('Add to cart failed:', error)
    }
  }

  // Show success state if enabled and either local success or last added item matches
  const shouldShowSuccess = showSuccessState && (
    showSuccess || 
    (lastAddedItem?.product?.id === productId)
  )

  return (
    <Button
      variant={variant}
      size={size}
      className={`transition-all duration-200 hover:scale-[1.02] active:scale-[0.98] ${className}`}
      onClick={handleAddToCart}
      disabled={isDisabled}
      aria-label={isLoading ? t('adding') : shouldShowSuccess ? t('added') : t('addToCart')}
    >
      {isLoading ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" aria-hidden="true" />
          {t('adding')}
        </>
      ) : shouldShowSuccess ? (
        <div className="animate-in zoom-in-95 duration-200">
          <Check className="mr-2 h-4 w-4 text-green-600" aria-hidden="true" />
          {t('added')}
        </div>
      ) : (
        <>
          {children ? (
            children
          ) : (
            <>
              <ShoppingCart className="mr-2 h-4 w-4 transition-transform group-hover:scale-110" aria-hidden="true" />
              {t('addToCart')}
            </>
          )}
        </>
      )}
    </Button>
  )
}

// Icon-only variant for compact spaces
export function AddToCartIconButtonV2({
  productId,
  quantity = 1,
  size = 'default',
  className,
  disabled = false,
}: Omit<AddToCartButtonV2Props, 'children' | 'variant'>) {
  const t = useTranslations('common')
  const { toast } = useToast()
  const [showSuccess, setShowSuccess] = useState(false)

  const addToCartMutation = useAddToCartMutation()
  const { openDrawer } = useCartDrawerActions()

  const isLoading = addToCartMutation.isPending
  const isDisabled = disabled || isLoading

  const handleAddToCart = async () => {
    if (isDisabled) return

    try {
      await addToCartMutation.mutateAsync({
        product_id: productId,
        quantity,
      })

      setShowSuccess(true)
      setTimeout(() => setShowSuccess(false), 2000)

      toast({
        title: t('added'),
        description: t('productAddedToCart'),
      })

      setTimeout(() => openDrawer(), 100)
    } catch (error) {
      console.error('Add to cart failed:', error)
    }
  }

  return (
    <Button
      variant="outline"
      size={size}
      className={`transition-all duration-200 hover:scale-[1.02] active:scale-[0.98] ${className}`}
      onClick={handleAddToCart}
      disabled={isDisabled}
      aria-label={isLoading ? t('adding') : showSuccess ? t('added') : t('addToCart')}
    >
      {isLoading ? (
        <Loader2 className="h-4 w-4 animate-spin" aria-hidden="true" />
      ) : showSuccess ? (
        <Check className="h-4 w-4 text-green-600 animate-in zoom-in-95 duration-200" aria-hidden="true" />
      ) : (
        <Plus className="h-4 w-4 transition-transform group-hover:scale-110" aria-hidden="true" />
      )}
    </Button>
  )
}

// Compact variant for product cards
export function AddToCartCompactButtonV2({
  productId,
  quantity = 1,
  className,
  disabled = false,
}: Omit<AddToCartButtonV2Props, 'children' | 'variant' | 'size'>) {
  return (
    <AddToCartButtonV2
      productId={productId}
      quantity={quantity}
      variant="outline"
      size="sm"
      className={`px-3 ${className}`}
      disabled={disabled}
      openDrawerOnAdd={true}
    >
      <ShoppingCart className="h-4 w-4" />
    </AddToCartButtonV2>
  )
}

// Specialized variants for common use cases
export function AddToCartQuickButtonV2({
  productId,
  quantity = 1,
  className,
  disabled = false,
}: {
  productId: string
  quantity?: number
  className?: string
  disabled?: boolean
}) {
  return (
    <AddToCartButtonV2
      productId={productId}
      quantity={quantity}
      variant="outline"
      size="sm"
      className={className}
      disabled={disabled}
      showSuccessState={false}
    >
      <Plus className="h-4 w-4" />
    </AddToCartButtonV2>
  )
}


