/**
 * @jest-environment node
 */

// Cart V2 API Route Tests
// Tests for Cart V2 API endpoints

import { describe, it, expect, beforeEach, jest } from '@jest/globals'
import { NextRequest } from 'next/server'
import { GET, POST, PUT, DELETE } from '../route'
import { CartError } from '../../../../lib/cart-v2/errors'
import type { Cart, CartItem } from '../../../../lib/cart-v2/types'

// Mock CartServiceV2
const mockCartService = {
  getCart: jest.fn(),
  addToCart: jest.fn(),
  updateCartItem: jest.fn(),
  removeFromCart: jest.fn(),
  clearCart: jest.fn(),
  syncGuestCartToUser: jest.fn()
}

// Mock Supabase client
const mockSupabaseClient = {
  auth: {
    getUser: jest.fn()
  }
}

// Mock session functions
jest.mock('../../../../lib/cart-session', () => ({
  getServerCartSessionId: jest.fn().mockResolvedValue('test-session-id')
}))

// Mock CartServiceV2
jest.mock('../../../../lib/cart-v2/server-service', () => ({
  CartServiceV2: jest.fn().mockImplementation(() => mockCartService)
}))

// Mock Supabase
jest.mock('../../../../lib/supabase/server', () => ({
  createClient: jest.fn().mockReturnValue(mockSupabaseClient)
}))

describe('Cart V2 API Routes', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('GET /api/cart-v2', () => {
    it('should return cart for authenticated user', async () => {
      const mockCart: Cart = {
        id: 'cart-1',
        user_id: 'user-1',
        session_id: null,
        status: 'active',
        total_amount: 25.99,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        items: []
      }

      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user-1' } },
        error: null
      })

      mockCartService.getCart.mockResolvedValue(mockCart)

      const request = new NextRequest('http://localhost:3000/api/cart-v2')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.cart).toEqual(mockCart)
      expect(mockCartService.getCart).toHaveBeenCalledWith({
        user_id: 'user-1',
        session_id: null
      })
    })

    it('should return cart for guest user', async () => {
      const mockCart: Cart = {
        id: 'cart-1',
        user_id: null,
        session_id: 'test-session-id',
        status: 'active',
        total_amount: 15.99,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        items: []
      }

      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null
      })

      mockCartService.getCart.mockResolvedValue(mockCart)

      const request = new NextRequest('http://localhost:3000/api/cart-v2')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.cart).toEqual(mockCart)
      expect(mockCartService.getCart).toHaveBeenCalledWith({
        user_id: null,
        session_id: 'test-session-id'
      })
    })

    it('should handle cart not found', async () => {
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user-1' } },
        error: null
      })

      mockCartService.getCart.mockResolvedValue(null)

      const request = new NextRequest('http://localhost:3000/api/cart-v2')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.cart).toBeNull()
    })

    it('should handle errors', async () => {
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user-1' } },
        error: null
      })

      const error = new CartError('DATABASE_ERROR', 'Database connection failed')
      mockCartService.getCart.mockRejectedValue(error)

      const request = new NextRequest('http://localhost:3000/api/cart-v2')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.error).toBe('Database connection failed')
      expect(data.code).toBe('DATABASE_ERROR')
    })
  })

  describe('POST /api/cart-v2', () => {
    it('should add item to cart', async () => {
      const mockCartItem: CartItem = {
        id: 'item-1',
        cart_id: 'cart-1',
        product_id: 'product-1',
        quantity: 2,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        product: {
          id: 'product-1',
          title: 'Test Coffee',
          price: 12.99,
          images: [],
          category: 'Caffè',
          slug: 'test-coffee',
          is_available: true
        }
      }

      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user-1' } },
        error: null
      })

      mockCartService.addToCart.mockResolvedValue(mockCartItem)

      const requestBody = {
        product_id: 'product-1',
        quantity: 2
      }

      const request = new NextRequest('http://localhost:3000/api/cart-v2', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: { 'Content-Type': 'application/json' }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.item).toEqual(mockCartItem)
      expect(mockCartService.addToCart).toHaveBeenCalledWith({
        product_id: 'product-1',
        quantity: 2,
        user_id: 'user-1',
        session_id: null
      })
    })

    it('should validate request body', async () => {
      const request = new NextRequest('http://localhost:3000/api/cart-v2', {
        method: 'POST',
        body: JSON.stringify({ invalid: 'data' }),
        headers: { 'Content-Type': 'application/json' }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toContain('product_id')
    })

    it('should handle validation errors', async () => {
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user-1' } },
        error: null
      })

      const error = new CartError('INVALID_QUANTITY', 'Quantity must be positive')
      mockCartService.addToCart.mockRejectedValue(error)

      const request = new NextRequest('http://localhost:3000/api/cart-v2', {
        method: 'POST',
        body: JSON.stringify({ product_id: 'product-1', quantity: -1 }),
        headers: { 'Content-Type': 'application/json' }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Quantity must be positive')
      expect(data.code).toBe('INVALID_QUANTITY')
    })
  })

  describe('PUT /api/cart-v2', () => {
    it('should update cart item', async () => {
      const mockCartItem: CartItem = {
        id: 'item-1',
        cart_id: 'cart-1',
        product_id: 'product-1',
        quantity: 3,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        product: {
          id: 'product-1',
          title: 'Test Coffee',
          price: 12.99,
          images: [],
          category: 'Caffè',
          slug: 'test-coffee',
          is_available: true
        }
      }

      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user-1' } },
        error: null
      })

      mockCartService.updateCartItem.mockResolvedValue(mockCartItem)

      const requestBody = {
        item_id: 'item-1',
        quantity: 3
      }

      const request = new NextRequest('http://localhost:3000/api/cart-v2', {
        method: 'PUT',
        body: JSON.stringify(requestBody),
        headers: { 'Content-Type': 'application/json' }
      })

      const response = await PUT(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.item).toEqual(mockCartItem)
      expect(mockCartService.updateCartItem).toHaveBeenCalledWith({
        item_id: 'item-1',
        quantity: 3,
        user_id: 'user-1',
        session_id: null
      })
    })

    it('should handle item not found', async () => {
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user-1' } },
        error: null
      })

      const error = new CartError('ITEM_NOT_FOUND', 'Cart item not found')
      mockCartService.updateCartItem.mockRejectedValue(error)

      const request = new NextRequest('http://localhost:3000/api/cart-v2', {
        method: 'PUT',
        body: JSON.stringify({ item_id: 'invalid-item', quantity: 1 }),
        headers: { 'Content-Type': 'application/json' }
      })

      const response = await PUT(request)
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.error).toBe('Cart item not found')
      expect(data.code).toBe('ITEM_NOT_FOUND')
    })
  })

  describe('DELETE /api/cart-v2', () => {
    it('should remove item from cart', async () => {
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user-1' } },
        error: null
      })

      mockCartService.removeFromCart.mockResolvedValue(undefined)

      const request = new NextRequest('http://localhost:3000/api/cart-v2?item_id=item-1')
      const response = await DELETE(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(mockCartService.removeFromCart).toHaveBeenCalledWith({
        item_id: 'item-1',
        user_id: 'user-1',
        session_id: null
      })
    })

    it('should validate item_id parameter', async () => {
      const request = new NextRequest('http://localhost:3000/api/cart-v2')
      const response = await DELETE(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toContain('item_id')
    })

    it('should handle item not found', async () => {
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user-1' } },
        error: null
      })

      const error = new CartError('ITEM_NOT_FOUND', 'Cart item not found')
      mockCartService.removeFromCart.mockRejectedValue(error)

      const request = new NextRequest('http://localhost:3000/api/cart-v2?item_id=invalid-item')
      const response = await DELETE(request)
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.error).toBe('Cart item not found')
      expect(data.code).toBe('ITEM_NOT_FOUND')
    })
  })
})
